<template>
  <div class="event-detail y-container no-padding">
    <div class="btn-nav" @click="handleNav">
      <svg-icon icon="icon-nav" style="margin-right: 4px"></svg-icon>
      <span class="blaze-text1">应急指挥一张图</span>
    </div>
    <div v-if="data" class="event-detail_info y-container no-padding">
      <el-image class="eventdetailicon" :src="require('@/assets/images/event-detail-icon.png')"></el-image>
      <div class="info">
        <div class="title blaze-text1">{{ data.disasterEventName }}</div>
        <div class="item">
          <div class="label">事件类型：</div>
          <el-tag :type="event_level_status[data.disasterLevel]" size="small" style="margin-right: 24px">{{ RESCUE_EVENT[data.disasterType] }}</el-tag>
          <div class="label">事件等级：</div>
          <div :class="['y-sign', event_level_status[data.disasterLevel]]" style="white-space: nowrap">
            {{ event_level[data.disasterLevel] }}
          </div>
          <div class="label">发生时间：</div>
          <div style="white-space: nowrap">{{ data.disasterTime }}</div>
          <div class="label">发生地点：</div>
          <div>{{ data.disasterLocation }}</div>
          <div class="label">接报时间：</div>
          <div style="white-space: nowrap">{{ data.createTime }}</div>
        </div>
        <div class="item">
          <div class="label">事件描述：</div>
          <div class="overflow">{{ data.disasterEventDesc }}</div>
        </div>
      </div>
    </div>
    <div class="event-detail_timeline y-container no-padding">
      <svg-icon @click.native="$refs.timelineScroll.xPos += 200" icon="left"></svg-icon>
      <div class="wrapper" ref="timeline">
        <template v-if="timelineData.length">
          <SeamlessScroll
            ref="timelineScroll"
            :data="timelineData"
            :classOption="{
              limitMoveNum: 4,
              step: 0.5,
              direction: 2,
            }"
            :scrollable="true">
            <div v-for="item in timelineData" class="item">
              <div class="icon"></div>
              <div class="time">{{ item.createTime }}</div>
              <div class="content blaze-text1">
                {{ item.taskStatus === "0" ? task_status[item.taskStatus] : `${task_status[item.taskStatus]}:${item.taskTitle}` }}
              </div>
            </div>
          </SeamlessScroll>
        </template>
        <el-empty v-else class="simple-empty">
          <div slot="image"></div>
        </el-empty>
      </div>
      <svg-icon @click.native="$refs.timelineScroll.xPos -= 200" icon="right"></svg-icon>
    </div>
    <div class="event-detail_body y-container no-padding">
      <div class="y-container--tight no-padding">
        <el-table :data="taskList" stripe height="100%" fit ref="table" style="width: 100%">
          <el-table-column prop="taskName" label="任务名称" min-width="80" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="teamName" label="所属机构" min-width="80" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="kilometer" label="当前里程" min-width="80" show-overflow-tooltip>
            <template v-if="scope.row.kilometer" slot-scope="scope">
              <el-link type="primary" :underline="false">{{ Math.round(Number(scope.row.kilometer) / 1000) }}公里</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="expectedTime" label="预计时间" min-width="80" show-overflow-tooltip>
            <template v-if="scope.row.expectedTime" slot-scope="scope">{{ Math.round(Number(scope.row.expectedTime) / 60) + "分钟" }}</template>
          </el-table-column>
          <el-table-column prop="leaderName" label="任务负责人" min-width="90" show-overflow-tooltip> </el-table-column>
          <el-table-column label="目的地经纬度" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.lon }},{{ scope.row.lat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="连线操作" min-width="80">
            <template slot-scope="scope">
              <el-link @click="handleConnection(scope.row)" type="primary" :underline="false" style="color: #00ffff">
                <svg-icon icon="connect" style="color: #00ffff" class="svgicon"></svg-icon>
                连线
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="y-container">
        <h3 class="y-title blaze-text1" style="margin-bottom: 16px">现场全息情报</h3>
        <div class="y-container no-padding">
          <template v-if="taskRecordDetail.length">
            <SeamlessScroll
              ref="seamlessScrollRef"
              style="width: 100%"
              :data="taskRecordDetail"
              :classOption="{
                limitMoveNum: 2,
                step: 0.5,
              }"
              :scrollable="true">
              <LiveItem v-for="(item, index) in taskRecordDetail" :key="index" :item="item" />
            </SeamlessScroll>
          </template>
          <el-empty v-else>
            <div slot="image"></div>
          </el-empty>
        </div>
      </div>
      <div class="y-container no-padding">
        <Map
          v-if="monitorData && data"
          :data="monitorData"
          :eventData="data"
          :overlay="false"
          :controlStyle="{
            right: '40px',
            bottom: '84px',
          }"
          :legendStyle="{
            bottom: '0',
            width: '100%',
            background: 'rgba(4,203,216, .1)',
          }" />
      </div>
    </div>
    <!-- WebRTC 视频连线功能已移除 -->
    <!-- <BaseDialog :visible.sync="connectionVisible" @close="closeWebrtc" :title="connectionData && connectionData.taskName">
      <div class="webrtc-video-box">
        ...WebRTC 相关内容已移除...
      </div>
    </BaseDialog> -->
  </div>
</template>

<script>
import LiveItem from "./LiveItem.vue";
// import Map from "@/views/event-monitor/components/map.vue";
// import { getAndSetDict, event_level_status } from "@/utils";
// import { cancelRoll } from "@/utils/table-roll";
// import SSE from "@/http/sse";

export default {
  components: {
    LiveItem,
    Map,
  },
  props: {
    data: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      timelineData: [],
      taskList: [],
      taskRecordDetail: [],
      monitorData: null,

      event_level: {},
      RESCUE_EVENT: {},
      // event_level_status,
      task_status: {
        0: "电话接报时间",
        1: "任务创建",
        2: "任务确认",
        4: "任务出发",
        5: "任务到达",
        3: "任务完成",
      },
    };
  },
  computed: {},
  created() {
    // getAndSetDict("event_level,RESCUE_EVENT", this);
  },
  // beforeDestroy() {
  //   cancelRoll(this.$refs.table);
  // },
  methods: {
    handleNav() {
      // TODO: 重构后需要重新实现事件监控页面导航
      // this.$router.push({
      //   path: "/event-monitor/" + this.data.disasterEventId,
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.event-detail {
  font-size: 14px;
  .btn-nav {
    position: absolute;
    top: 0px;
    right: 112px;
    padding: 16px;
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(180deg, #ffffff 8%, #affaff 62%, #04cbd8 95%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    border-radius: 0px 0px 8px 8px;
    box-sizing: border-box;
    border: 1px solid $themeColor;
    border-top: none;
    box-shadow: inset 0px 0px 16px 0px rgba(4, 203, 216, 0.5);
    cursor: pointer;
  }

  .event-detail_info {
    @include flex-row;
    margin-top: 16px;
    flex: 0 0 max-content;

    .info {
      @include flex-col;
      flex: 1;
      align-items: flex-start;
      gap: 4px;

      .title {
        font-size: 24px;
        font-weight: bold;
      }

      .item {
        @include flex-row;
        justify-content: flex-start;
        color: $txtColor-reverse;

        .label {
          font-size: 14px;
          color: $txtColor-light;
          width: 70px;
          white-space: nowrap;
        }

        div:not(.label) {
          margin-right: 24px;

          &.overflow {
            @include text-overflow(1);
          }
        }
      }
    }
  }

  .event-detail_timeline {
    @include flex-row;
    flex: 0 0 max-content;
    margin-top: 16px;
    padding: 4px 0;
    background: linear-gradient(270deg, rgba(4, 203, 216, 0) 0%, rgba(4, 203, 216, 0.05) 49%, rgba(4, 203, 216, 0) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(4, 203, 216, 0) 0%, rgba(4, 203, 216, 0.2) 49%, rgba(4, 203, 216, 0) 100%) 1;

    .wrapper {
      // @include flex-row;
      flex: 1;
      // justify-content: flex-start;
      margin: 0 8px;
      padding-top: 4px;
      height: 100%;
      border: 1px solid transparentize($themeColor, 0.8);
      border-top: none;
      border-bottom: none;
      overflow: hidden;

      .item {
        float: left;
        @include flex-row;

        &:not(:last-child) {
          margin-right: 8px;

          &::after {
            content: "";
            display: block;
            width: 64px;
            height: 1px;
            border-top: 1px dashed transparentize($themeColor, 0.5);
          }
        }

        .icon {
          margin-right: 4px;
          width: 24px;
          height: 24px;
          background: url("@/assets/images/timeline-icon.png") no-repeat center/150%;
        }

        .time {
          margin-right: 8px;
          font-size: 14px;
          color: $txtColor-light;
        }

        .content {
          margin-right: 8px;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }

    .svg-icon {
      font-size: 32px;
      cursor: pointer;
    }
  }

  .event-detail_body {
    display: grid;
    gap: 24px;
    grid-template-columns: minmax(710px, 1fr) minmax(860px, 1fr);
    grid-template-rows: minmax(132px, 1fr) minmax(410px, 1fr);
    grid-template-areas:
      "a c"
      "b c";
    margin-top: 24px;

    > div {
      border: 1px solid;
      border-radius: 4px;
      border-image: linear-gradient(180deg, rgba(4, 203, 216, 0.2) 0%, rgba(4, 203, 216, 0.1) 100%) 2;

      &:nth-child(1) {
        grid-area: a;
      }
      &:nth-child(2) {
        grid-area: b;
        padding: 16px;
        background: transparentize($themeColor, 0.95);
      }
      &:nth-child(3) {
        grid-area: c;
      }
    }
  }

  .el-empty.simple-empty::v-deep {
    padding: 0;

    .el-empty__description {
      margin: 0;
    }
  }
}

/* WebRTC 相关样式已移除 */
/* .webrtc-video-box { ... } */
.eventdetailicon {
  width: 80px;
  height: 80px;
  margin-right: 24px;
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.svgicon {
  font-size: 16px;
}
</style>
