<template>
  <div class="bar-chart y-container no-padding">
    <BaseDelay delay="1000">
      <h3 class="bar-chart_title blaze-text">AI中台数据分析</h3>
    </BaseDelay>
    <div
      class="bar-chart_map chart-container y-container no-padding"
      ref="chart-container"
    ></div>

    <!-- 下钻图表弹窗 -->
    <DrillDownChart
      :visible.sync="dialogVisible"
      :selected-time-range="selectedTimeRange"
      :selected-data="selectedData"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import { getCallNumbers } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";
import DrillDown<PERSON>hart from "./DrillDownChart.vue";

export default {
  name: "Bar<PERSON>hart", // 更新组件名称
  components: {
    DrillDownChart
  },
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      // 通话时长分布数据
      callDurationStats: [],
      activeBar: null, // 当前选中的时长区间
      delayAppear: 1000,

      callNumbers: [],
      
      // 弹窗相关数据
      dialogVisible: false,
      selectedTimeRange: "",
      selectedData: {}
    };
  },
  computed: {
    option() {
      return {
        title: {
          text: "",
          left: "center",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: (params) => {
            const data = params[0];
            return `${data.name}<br/>挂断数量：${data.value}`;
          },
        },
        xAxis: {
          type: "category",
          name: '时间段',
          data: this.getBarData.time,
          axisLabel: {
            color: "#fff",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "#4a90e2",
            },
          },
          
        },
        yAxis: {
          type: "value",
          name: "挂断数量",
          axisLabel: {
            color: "#4a90e2",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "#4a90e2",
            },
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255, 255, 255, 0.1)",
            },
          },
        },
        series: [
          {
            type: "bar",
            data: this.getBarData.num,
            itemStyle: {
              color: (params) => {
                // 高亮选中的柱子
                if (this.activeBar === params.dataIndex) {
                  return "#00ffff"; // 青色，与系统主题更搭配
                }
                return "#4a90e2";
              },
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: "#00ffff", // 青色，与系统主题更搭配
              },
            },
          },
        ],
      };
    },
    getBarData() {
      const time = this.callNumbers.map((item) => {
        // 将数字格式化为 "xx时" 格式
        const hour = item.CALL_GENERATION_TIME;
        return `${hour}时`;
      });
      const num = this.callNumbers.map((item) => item.NUM);
      return { time, num };
    },
  },
  created() {
    this.fetchCallNumbers();
  },
  methods: {
    async fetchCallNumbers() {
      const [_, res] = await getCallNumbers();
      if (res) {
        this.callNumbers = res.data.sort(
          (a, b) => a.CALL_GENERATION_TIME - b.CALL_GENERATION_TIME
        );
      }
    },

    // 重写 getChart 方法以添加柱形图点击事件
    getChart() {
      if (!this.$refs['chart-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();
      let chart = this.$echarts.init(this.$refs['chart-container']);
      this.chart = chart;
      chart.setOption(this.option, true);
      
      // 添加柱形图点击事件监听器
      chart.on('click', (params) => {
        if (params.componentType === 'series' && params.seriesType === 'bar') {
          this.handleBarClick(params);
        }
      });

      this.$nextTick(this.resizeHandler);
    },

    // 处理柱形图点击事件
    handleBarClick(params) {
      console.log('柱形图点击事件:', params);
      
      // 获取点击的时间段信息
      const timeRange = params.name; // 这是格式化后的 "xx时"
      const dataIndex = params.dataIndex;
      const value = params.value;
      
      // 获取原始的时间数据（数字格式）
      const originalTimeValue = this.callNumbers[dataIndex]?.CALL_GENERATION_TIME;
      
      // 设置选中的数据
      this.selectedTimeRange = timeRange;
      this.selectedData = {
        timeRange: timeRange,
        originalTimeValue: originalTimeValue, // 传递原始数字值给API
        dataIndex: dataIndex,
        value: value,
        // 可以添加更多需要传递给下钻图表的数据
        originalData: this.callNumbers[dataIndex] || {}
      };
      
      // 显示弹窗
      this.dialogVisible = true;
      
      // 高亮选中的柱子
      this.activeBar = dataIndex;
    },

    // 处理弹窗关闭事件
    handleDialogClose() {
      this.dialogVisible = false;
      this.selectedTimeRange = "";
      this.selectedData = {};
      this.activeBar = null;
    },

    lineMaxHeight() {
      return 0.05; 
    },
    // 柱状体的主干
    lineData() {
      return this.data.map((item) => {
        if (item.name == this.activeArea) {
          return {
            coords: [
              this.centerData[item.name],
              [
                this.centerData[item.name][0],
                this.centerData[item.name][1] + 1 * 0.11,
              ],
              item.name,
            ],
          };
        } else {
          return {
            coords: [
              this.centerData[item.name],
              [
                this.centerData[item.name][0],
                this.centerData[item.name][1] + 1 * 0.05,
              ],
              item.name,
            ],
          };
        }
      });
    },
    // 柱状体的顶部
    scatterData() {
      return this.data.map((item) => {
        return [
          this.centerData[item.name][0],
          this.centerData[item.name][1] + 1 * this.lineMaxHeight(),
          item,
        ];
      });
    },
    // 柱状体的底部
    scatterData2() {
      return this.data.map((item) => {
        return {
          name: item.areaName,
          value: this.centerData[item.name],
          code: item.name,
        };
      });
    },
    handlefilterbar(active) {
      this.activefilterbar = active;
    },
    getData() {
      // 默认使用事件分布模式（tabs 功能已移除）
      return this.eventList
        .filter(
          (item) =>
            item.disasterAreaCode && item.disasterAreaCode.startsWith("4101")
        )
        .map((item) => ({
          ...item,
          name: item.disasterEventName,
          level: item.disasterLevel,
          value: [Number(item.disasterLon), Number(item.disasterLat)],
        }));
    },
    turnDown(params) {
      console.log("turnDown", params);
      let { dataIndex } = params;
      this.chart.dispatchAction({
        type: "downplay",
        dataIndex,
      });
      this.activeArea = null;
    },
    turnOn(params) {
      console.log("turnOn", params);
      let { dataIndex } = params;
      this.data.forEach(() => {
        this.chart.dispatchAction({
          type: "downplay",
        });
      });
      this.chart.dispatchAction({
        type: "highlight",
        dataIndex,
      });
      this.activeArea = params.data[2].name;
    },
  },
};
</script>

<style lang="scss" scoped>
.bar-chart {
  position: relative;
  overflow: visible;
  background: url("@/assets/images/area-chart-bg.png") no-repeat center
    bottom/100% 62.5%;
  width: 1130px;
  height: 676px;
  .bar-chart_title {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    font-weight: bold;
  }

  .bar-chart_map {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translate(-50%); // rotate(22deg)
    width: 990px;
    height: 698px;
  }
}

.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 600;
}
.filterclass {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;
  gap: 16px;
  .filterinput {
    width: 350px;
  }
}
.btnbox {
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  height: 40px;
  background: #054598;
  border-radius: 4px;
  cursor: pointer;
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
canvas {
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}
</style>
