<template>
  <div id="layout">
    <transition name="header-appear" appear>
      <div class="layout_header">
        <h2 class="layout_header-title blaze-text" v-text="title"></h2>
      </div>
    </transition>
    <!-- <div class="layout-line"></div>
    <div class="layout-line reverse"></div> -->
    <div class="layout-slogan blaze-text">数据可视 · 智能预见 · 驱动增长</div>
    <div class="layout-slogan reverse blaze-text">{{ date | formatDate }}</div>
    <img src="@/assets/images/layout-slogan-bg-left.png" alt="" class="layout-slogan-left" />
    <img src="@/assets/images/layout-slogan-bg-right.png" alt="" class="layout-slogan-right" />
    <!-- <transition name="footer-appear" appear>
      <div class="layout-footer"></div>
    </transition> -->
    <div class="layout-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "标题",
    },
  },
  data() {
    return {
      date: new Date(),
      timer: null,
    };
  },
  computed: {},
  created() {
    this.timer = setInterval(() => {
      this.date = new Date();
    }, 1000);
  },
  beforeDestroy() {
    if (this.timer) clearInterval(this.timer);
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
#layout {
  @include full;
  position: relative;
  padding: 86px 0px 0;
  background: #000 url("@/assets/images/layout-bg.png") no-repeat center/cover;

  .layout_header {
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 100;
    width: 1232px;
    height: 64px;
    transform: translate(-50%, 0);
    background: url("@/assets/images/layout-header-bg.png") no-repeat top center/cover;

    .layout_header-title {
      padding-top: 0px;
      font-size: 40px;
      font-weight: bold;
      text-align: center;
      letter-spacing: 0.04em;
      // text-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
    }
  }

  .layout-content {
    position: relative;
    z-index: 2;
    @include full;
  }

  .layout-footer {
    position: absolute;
    bottom: 0;
    left: 50%;
    z-index: 100;
    width: 1000px;
    height: 90px;
    transform: translate(-50%, 0);
    background: url("@/assets/images/layout-footer-bg.png") no-repeat bottom center/cover;
  }

  .layout-line {
    position: absolute;
    right: 32px;
    bottom: 32px;
    width: 32px;
    height: 966px;
    transform: rotate(180deg);
    background: url("@/assets/images/layout-side-line.png") no-repeat center/cover;

    &.reverse {
      left: 32px;
      transform: rotate(0);
    }
  }
  .layout-slogan-left {
    position: absolute;
    top: 60px;
    left: 24px;
    width: 262px;
    height: 4px;
  }
  .layout-slogan-right {
    position: absolute;
    top: 60px;
    right: 20px;
    width: 262px;
    height: 4px;
  }
  .layout-slogan {
    position: absolute;
    top: 20px;
    left: 32px;
    width: max-content;
    font-size: 18px;
    font-weight: bold;
    // text-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
    &.reverse {
      left: unset;
      right: 36px;
    }
  }

  .header-appear-enter-active,
  .header-appear-leave-active,
  .footer-appear-enter-active,
  .footer-appear-leave-active {
    transition: all 0.5s ease-out;
  }

  .header-appear-enter,
  .header-appear-leave-to {
    top: -128px;
  }

  .footer-appear-enter,
  .footer-appear-leave-to {
    bottom: -90px;
  }
}
</style>
