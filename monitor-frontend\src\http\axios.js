import Vue from 'vue'
import axios from 'axios'
import { handleNetworkError, handleAuthError, handleGeneralError } from './handlers.js'

const config = {
  baseURL: process.env.VUE_APP_BASE_API || '',
  timeout: 5 * 1000, // Timeout
}

// 高封装
const service = axios.create(config)

service.interceptors.request.use(
  (config) => {
    return {
      ...config,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      }
    }
  },
  (error) => {
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  (response) => {
    // console.log('axios-response',response)
    if (response.status !== 200) return Promise.reject(response)
    const flag = handleGeneralError(response.data?.state, response.data?.msg)
    if (!flag) return Promise.reject(response)
    return response.data
  },
  (error) => {
    // console.log('axios-response-error',error)
    if (error?.response.status === 401) {
      handleAuthError(error?.response)
    } else {
      handleNetworkError(error?.response.status)
    }
    return Promise.reject(error)
  }
)


// 低封装
const _axios = axios.create(config)

_axios.interceptors.request.use(
  (config) => {
    // config = handleRequestHeader(config)
    // config = handleAuth(config)
    // console.log(config)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)


Object.defineProperties(Vue.prototype, {
  $api: {
    get() {
      return service
    }
  },
  $axios: {
    get() {
      return _axios
    }
  },
})

export default service