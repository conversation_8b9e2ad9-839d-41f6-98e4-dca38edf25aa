export default {
  namespaced: true,
  state: {
    dateRange: [],
    zIndex: 1000,
  },
  mutations: {
    SET_DATE_RANGE(state, dateRange) {
      state.dateRange = dateRange
    },
    INCREMENT_Z_INDEX(state) {
      state.zIndex += 1
    }
  },
  actions: {
    setDateRange({ commit }, dateRange) {
      commit('SET_DATE_RANGE', dateRange)
    },
    incrementZIndex({ commit }) {
      commit('INCREMENT_Z_INDEX')
    }
  },
} 