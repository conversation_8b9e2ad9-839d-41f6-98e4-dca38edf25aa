<template>
  <div class="maincontent">
    <div class="eventtop">
      <div class="eventitem">
        <img src="@/assets/images/eventreceive.png" alt="" class="imgevent" />
        <div class="eventtext">
          <div class="eventtextitem">意向总量</div>
          <div class="eventtextitem1">{{ totalIntentions }}</div>
        </div>
      </div>
      <div class="eventitem">
        <img src="@/assets/images/eventtotal.png" alt="" class="imgevent" />
        <div class="eventtext">
          <div class="eventtextitem">分类数量</div>
          <div class="eventtextitem2">{{ intentionCategories }}</div>
        </div>
      </div>
    </div>
    <div class="tableheight">
      <el-table :data="userIntentionData" height="100%" fit ref="table" stripe>
        <el-table-column prop="DEMAND_LABEL" label="意向标签" show-overflow-tooltip min-width="50%">
          <template slot-scope="scope">
            <div class="bar">
              <!-- 显示意向标签 -->
              <div class="level-label">{{ scope.row.DEMAND_LABEL }}</div>
              <div class="progress-container">
                <!-- 相对定位容器 -->
                <!-- 自定义进度条 -->
                <el-progress
                  :percentage="getPercentage(scope.row.NUM)"
                  :stroke-width="8"
                  :class="['custom-progress', `progress-${scope.$index}`]"
                  :show-text="false"
                />
                <!-- 动态指示块 -->
                <div class="custom-indicator" :style="{ left: calcPosition(scope.row.NUM), background: getIndicatorColor(scope.$index) }"></div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="NUM" label="数量" min-width="25%">
          <template slot-scope="scope">
            <div class="color1">
              {{ parseInt(scope.row.NUM).toLocaleString() }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="percentage" label="占比" min-width="25%">
          <template slot-scope="scope">
            <div class="color2">
              {{ getIntentionPercentage(scope.row.NUM) }}%
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { initRoll, cancelRoll } from "@/utils/table-roll";
import { getUserIntentionLabel } from "@/api";

export default {
  name: "DataBlocktable",
  inject: ["dateRange"],
  data() {
    return {
      userIntentionData: [],
      loading: false
    };
  },
  watch: {
    "userIntentionData.length": {
      handler(length) {
        if (length > 4) {
          this.$nextTick(() => {
            initRoll(this.$refs.table);
          });
        }
      },
      immediate: true,
    },
  },
  created() {
    this.fetchUserIntentionData();
  },
  beforeDestroy() {
    cancelRoll(this.$refs.table);
  },
  computed: {
    // 计算意向总量
    totalIntentions() {
      if (!this.userIntentionData || this.userIntentionData.length === 0) return 0;
      return this.userIntentionData.reduce((total, item) => {
        return total + parseInt(item.NUM || 0);
      }, 0);
    },

    // 计算分类数量
    intentionCategories() {
      return this.userIntentionData ? this.userIntentionData.length : 0;
    },

    maxValue() {
      // 处理空数组情况
      if (this.userIntentionData.length === 0) return 0;
      const values = this.userIntentionData.map((i) => Number(i.NUM));
      return Math.max(...values) || 0;
    },

    maxProgress() {
      const max = this.maxValue;
      if (max <= 0) return 100; // 默认最大值

      // 优化基数计算逻辑
      const exponent = Math.floor(Math.log10(max));
      const base = Math.pow(10, exponent);
      return max < base ? base : Math.ceil(max / base) * base;
    },
  },
  methods: {
    // 获取用户意向数据
    async fetchUserIntentionData() {
      this.loading = true;
      try {
        const [error, response] = await getUserIntentionLabel();

        if (error) {
          console.error("获取用户意向数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.userIntentionData = [];
        } else if (response && response.data) {
          console.log("用户意向数据获取成功:", response.data);
          // 按 NUM 从大到小排序
          this.userIntentionData = response.data.sort((a, b) => parseInt(b.NUM) - parseInt(a.NUM));

          // 调试信息
          console.log("意向总量:", this.totalIntentions);
          this.userIntentionData.forEach((item) => {
            const percentage = this.getPercentage(item.NUM);
            const intentionPercentage = this.getIntentionPercentage(item.NUM);
            console.log(`${item.DEMAND_LABEL}: ${item.NUM} -> 进度条:${percentage}% 占比:${intentionPercentage}%`);
          });
        } else {
          console.warn("用户意向数据为空");
          this.userIntentionData = [];
        }
      } catch (error) {
        console.error("获取用户意向数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.userIntentionData = [];
      } finally {
        this.loading = false;
      }
    },

    // 计算意向占比
    getIntentionPercentage(value) {
      if (this.totalIntentions <= 0) return 0;
      const percentage = (parseInt(value) / this.totalIntentions) * 100;
      return percentage.toFixed(1);
    },

    // 获取指示器颜色
    getIndicatorColor(index) {
      const colors = [
        'linear-gradient(to bottom, #ff6b6b 0%, #ff8e8e 100%)', // 红色渐变
        'linear-gradient(to bottom, #00ffff 0%, #66ccff 100%)', // 青色渐变
        'linear-gradient(to bottom, #ffa726 0%, #ffcc80 100%)', // 橙色渐变
        'linear-gradient(to bottom, #4caf50 0%, #81c784 100%)', // 绿色渐变
        'linear-gradient(to bottom, #9c27b0 0%, #ba68c8 100%)', // 紫色渐变
        'linear-gradient(to bottom, #2196f3 0%, #64b5f6 100%)', // 蓝色渐变
        'linear-gradient(to bottom, #ff9800 0%, #ffb74d 100%)', // 深橙渐变
        'linear-gradient(to bottom, #607d8b 0%, #90a4ae 100%)'  // 灰蓝渐变
      ];
      return colors[index % colors.length];
    },

    // 进度条相关 - 基于数据占比计算
    getPercentage(value) {
      if (this.totalIntentions <= 0) return 0; // 防止除零
      const percentage = (parseInt(value) / this.totalIntentions) * 100;
      // 确保返回合法数值，范围在 0-100 之间
      return Math.min(Math.max(Number(percentage.toFixed(1)), 0), 100);
    },
    // 计算指示块位置 - 基于数据占比计算
    calcPosition(value) {
      if (this.totalIntentions <= 0) return '0px';
      const percentage = (parseInt(value) / this.totalIntentions) * 100;
      // 解决边界溢出问题（当100%时防止溢出）
      const safePercent = Math.min(Math.max(percentage, 0), 100);
      return `calc(${safePercent}% - 2px)`; // 微调偏移量
    },
  },
};
</script>

<style lang="scss" scoped>
.maincontent {
  margin-bottom: 12px;
  width: 100%;
  .eventtop {
    display: flex;
    align-items: center;
    gap: 16px;
    margin: 16px 0 12px;
    .imgevent {
      width: 56px;
      height: 100%;
    }
    .eventitem {
      padding-left: 16px;
      width: 254px;
      height: 72px;
      border-radius: 4px;
      opacity: 1;
      background: linear-gradient(180deg, rgba(0, 128, 255, 0.1) 0%, rgba(0, 128, 255, 0) 100%);
      display: flex;
      align-items: center;
      gap: 8px;
      .eventtext {
        .eventtextitem {
          font-size: 14px;
          color: #ffffff;
          margin-bottom: 8px;
          margin-top: 16px;
        }
        .eventtextitem1 {
          color: #00ffff;
          font-size: 24px;
          font-weight: bold;
        }
        .eventtextitem2 {
          color: #00aaff;
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }
}
.color1 {
  font-size: 16px;
  font-weight: bold;
  color: #00aaff;
}
.color2 {
  font-size: 16px;
  font-weight: bold;
  color: #00ffff;
}
.bar {
  display: flex;
  align-items: center;
  gap: 16px;
}
.progress-container {
  position: relative;
  width: 100%;
}

/* 进度条样式 */
.custom-progress {
  position: absolute;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);

  ::v-deep .el-progress-bar__outer {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  ::v-deep .el-progress-bar__inner {
    border-radius: 0 100px 100px 0;
  }
}

/* 为每个进度条设置不同颜色 */
.progress-0 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #ff6b6b 0%, #ff8e8e 50%, #ffb3b3 100%) !important; /* 红色渐变 */
}

.progress-1 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #00aaff 0%, #00ffff 50%, #66ccff 100%) !important; /* 青色渐变 */
}

.progress-2 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #ffa726 0%, #ffb74d 50%, #ffcc80 100%) !important; /* 橙色渐变 */
}

.progress-3 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #4caf50 0%, #66bb6a 50%, #81c784 100%) !important; /* 绿色渐变 */
}

.progress-4 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #9c27b0 0%, #ab47bc 50%, #ba68c8 100%) !important; /* 紫色渐变 */
}

.progress-5 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #2196f3 0%, #42a5f5 50%, #64b5f6 100%) !important; /* 蓝色渐变 */
}

.progress-6 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #ff9800 0%, #ffa726 50%, #ffb74d 100%) !important; /* 深橙渐变 */
}

.progress-7 ::v-deep .el-progress-bar__inner {
  background-image: linear-gradient(90deg, #607d8b 0%, #78909c 50%, #90a4ae 100%) !important; /* 灰蓝渐变 */
}

/* 额外的全局样式确保轨道颜色生效 */
.maincontent .el-progress-bar__outer {
  background-color: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 指示块样式 */
.custom-indicator {
  position: absolute;
  width: 4px;
  height: 14px;
  /* background 通过内联样式动态设置 */
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
  top: 50%;
  transform: translate(-1px, -50%); /* 微调居中 */
  z-index: 2;
  transition: left 0.3s ease;
}

/* 等级文本 */
.level-label {
  color: #fff; /* 根据背景色调整 */
  font-size: 14px;
}
.tableheight {
  height: 280px;
}
</style>

<style lang="scss">
/* 全局样式，不使用 scoped，确保 Element UI 进度条轨道颜色生效 */
.maincontent .custom-progress .el-progress-bar__outer {
  background-color: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  height: 8px !important;
}
</style>
