<template>
  <Layout title="中移指挥调度大屏">
    <div class="monitor-container">
      <div class="left-panel y-container pdr">
        <BaseDelay delay="1100">
          <transition-group class="y-container no-padding" name="fade-in-right" appear>
            <Titlecomponent key="overview-title" title="投诉分类统计" />
            <GlobalPanel key="global-panel" :data="overview" />
            <Titlecomponent key="channel-title" title="是否由机器人接通" />
            <ChannelChart key="channel-chart" :data="phoneRegisterCount" />
            <!-- <Titlecomponent key="undertake-title" title="是否由机器人接通" />
            <UndertakePanel key="undertake-panel" :data="taskOverview" /> -->
          </transition-group>
        </BaseDelay>
      </div>

      <div class="center-panel y-container">
        <BaseDelay delay="300">
          <transition name="area-appear" appear>
            <BarChart />
          </transition>
        </BaseDelay>
      </div>

      <div class="right-panel y-container pdl">
        <BaseDelay delay="1100">
          <transition-group class="y-container no-padding" name="fade-in-left" appear>
            <!-- <EventList key="event-list" style="margin-top: 24px; margin-bottom: 40px" /> -->
            <Titlecomponent key="eventdataoverview" title="用户意向" />
            <DataBlocktable key="disasterEvent" :disasterEventStats="disasterEventStats" :totalEvents="totalEvents" :totalCalls="totalCalls" />
            <!-- <Titlecomponent key="reporttype" title="接报类型" />
            <TypeBlocktable key="typeevent" :eventTypeStats="eventTypeStats" /> -->
            <Titlecomponent key="important-title" title="业务关注点" />
            <ImportantEventList key="important-list" />
          </transition-group>
        </BaseDelay>
      </div>
    </div>
  </Layout>
</template>

<script>
import { computed } from "vue";
import dayjs from "dayjs";
import Layout from "@/layout";
import ImportantEventList from "./components/ImportantEventList";
import BarChart from "./components/BarChart";
import GlobalPanel from "./components/GlobalPanel";
import ChannelChart from "./components/ChannelChart";
// import UndertakePanel from "./components/UndertakePanel";
import DataBlocktable from "./components/DataBlocktable.vue";
// import TypeBlocktable from "./components/TypeBlocktable.vue";

export default {
  components: {
    Layout,
    // EventList,
    ImportantEventList,
    BarChart,
    GlobalPanel,
    ChannelChart,
    // UndertakePanel,
    DataBlocktable,
    // TypeBlocktable,
  },
  data() {
    return {
      dateRange: this.initDate(),

      // 左侧数据
      sse: null,
      overview: {},
      phoneRegisterCount: [],
      taskOverview: {
        totalStats: {},
        centerStats: [],
      },
      //
      disasterEventStats: [],
      eventTypeStats: [],
      totalEvents: 0,
      totalCalls: 0,

      // dict
      RESCUE_EVENT: {},
      event_level: {},
    };
  },
  provide() {
    return {
      dateRange: computed(() => this.dateRange),
    };
  },
  methods: {
    initDate() {
      let today = dayjs().format("YYYY-MM-DD");
      let monthAgo = dayjs().subtract(1, "month").format("YYYY-MM-DD");
      return [monthAgo, today];
    },
    closeStream() {
      if (this.sse) {
        this.sse.close();
        this.sse = null;
      }
    },
    handleStreamData(res) {
      console.log("handleStreamData", res);
      if (res.data) {
        const { overview, phoneRegisterCount, taskOverview } = res.data;
        this.overview = overview || {};
        this.phoneRegisterCount = phoneRegisterCount || [];
        this.taskOverview = taskOverview || {
          totalStats: {},
          centerStats: [],
        };
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.monitor-container {
  @include full;
  position: relative;

  .center-panel {
    @include flex-center;
    position: relative;

    // .area-chart {
    //   position: absolute;
    //   top: 130px;
    //   left: 50%;
    //   transform: translateX(-50%);
    //   width: 1460px;
    //   height: 880px;
    // }

    .area-appear-enter-active,
    .area-appear-leave-active {
      transition: all 0.5s ease-out;
    }

    .area-appear-enter,
    .area-appear-leave-to {
      transform: translateX(-50%) scale(0);
    }
  }

  .left-panel {
    position: absolute;
    top: 0;
    left: 0;
    width: 520px;
    height: 980px;
    background: url("@/assets/images/left-panel-bg.png") no-repeat center center / calc(100% + 70px) calc(100% + 24px);
    z-index: 10;
  }

  .right-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 520px;
    height: 980px;
    background: url("@/assets/images/right-panel-bg.png") no-repeat center center / calc(100% + 70px) calc(100% + 24px);
    z-index: 10;
  }
  .pdl {
    padding-left: 72px;
    padding-top: 16px;
  }
  .pdr {
    padding-right: 72px;
    padding-top: 16px;
  }
}
</style>
