<template>
  <div class="bar-chart y-container no-padding">
    <BaseDelay delay="1000">
      <h3 class="bar-chart_title blaze-text">AI中台数据分析</h3>
    </BaseDelay>
    <div
      class="bar-chart_map chart-container y-container no-padding"
      ref="chart-container"
    ></div>
  </div>
</template>

<script>
import { getCallNumbers } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";

export default {
  components: {},
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      // 通话时长分布数据
      callDurationStats: [],
      activeBar: null, // 当前选中的时长区间
      delayAppear: 1000,

      callNumbers: [],
    };
  },
  computed: {
    option() {
      return {
        title: {
          text: "",
          left: "center",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: (params) => {
            const data = params[0];
            return `${data.name}<br/>挂断数量：${data.value}`;
          },
        },
        xAxis: {
          type: "category",
          name: '时间段',
          data: this.getBarData.time,
          axisLabel: {
            color: "#fff",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "#4a90e2",
            },
          },
          
        },
        yAxis: {
          type: "value",
          name: "挂断数量",
          axisLabel: {
            color: "#4a90e2",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "#4a90e2",
            },
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255, 255, 255, 0.1)",
            },
          },
        },
        series: [
          {
            type: "bar",
            data: this.getBarData.num,
            itemStyle: {
              color: (params) => {
                // 高亮选中的柱子
                if (this.activeBar === params.dataIndex) {
                  return "#ff6b6b";
                }
                return "#4a90e2";
              },
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: "#ff6b6b",
              },
            },
          },
        ],
      };
    },
    getBarData() {
      const time = this.callNumbers.map((item) => item.CALL_GENERATION_TIME);
      const num = this.callNumbers.map((item) => item.NUM);
      return { time, num };
    },
  },
  created() {
    this.fetchCallNumbers();
  },
  methods: {
    async fetchCallNumbers() {
      const [_, res] = await getCallNumbers();
      if (res) {
        this.callNumbers = res.data.sort(
          (a, b) => a.CALL_GENERATION_TIME - b.CALL_GENERATION_TIME
        );
      }
    },

    lineMaxHeight() {
      return 0.05; 
    },
    // 柱状体的主干
    lineData() {
      return this.data.map((item) => {
        if (item.name == this.activeArea) {
          return {
            coords: [
              this.centerData[item.name],
              [
                this.centerData[item.name][0],
                this.centerData[item.name][1] + 1 * 0.11,
              ],
              item.name,
            ],
          };
        } else {
          return {
            coords: [
              this.centerData[item.name],
              [
                this.centerData[item.name][0],
                this.centerData[item.name][1] + 1 * 0.05,
              ],
              item.name,
            ],
          };
        }
      });
    },
    // 柱状体的顶部
    scatterData() {
      return this.data.map((item) => {
        return [
          this.centerData[item.name][0],
          this.centerData[item.name][1] + 1 * this.lineMaxHeight(),
          item,
        ];
      });
    },
    // 柱状体的底部
    scatterData2() {
      return this.data.map((item) => {
        return {
          name: item.areaName,
          value: this.centerData[item.name],
          code: item.name,
        };
      });
    },
    handlefilterbar(active) {
      this.activefilterbar = active;
    },
    getData() {
      // 默认使用事件分布模式（tabs 功能已移除）
      return this.eventList
        .filter(
          (item) =>
            item.disasterAreaCode && item.disasterAreaCode.startsWith("4101")
        )
        .map((item) => ({
          ...item,
          name: item.disasterEventName,
          level: item.disasterLevel,
          value: [Number(item.disasterLon), Number(item.disasterLat)],
        }));
    },
    turnDown(params) {
      console.log("turnDown", params);
      let { seriesIndex, dataIndex } = params;
      this.chart.dispatchAction({
        type: "downplay",
        dataIndex,
      });
      this.activeArea = null;
    },
    turnOn(params) {
      console.log("turnOn", params);
      let { seriesIndex, dataIndex } = params;
      this.data.forEach((v, i) => {
        this.chart.dispatchAction({
          type: "downplay",
        });
      });
      this.chart.dispatchAction({
        type: "highlight",
        dataIndex,
      });
      this.activeArea = params.data[2].name;
    },
  },
};
</script>

<style lang="scss" scoped>
.bar-chart {
  position: relative;
  overflow: visible;
  background: url("@/assets/images/area-chart-bg.png") no-repeat center
    bottom/100% 62.5%;
  width: 1130px;
  height: 676px;
  .bar-chart_title {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    font-weight: bold;
  }

  .bar-chart_map {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translate(-50%); // rotate(22deg)
    width: 990px;
    height: 698px;
  }
}

.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 600;
}
.filterclass {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;
  gap: 16px;
  .filterinput {
    width: 350px;
  }
}
.btnbox {
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  height: 40px;
  background: #054598;
  border-radius: 4px;
  cursor: pointer;
}
.blaze-text1 {
  background: linear-gradient(180deg, #ffffff 8%, #a1e0ff 63%, #57c7ff 95%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
canvas {
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}
</style>
