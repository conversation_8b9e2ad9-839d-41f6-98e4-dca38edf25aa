import { debounce } from '@/utils'

const documentWidth = document.documentElement.clientWidth

// echarts专用mixin
export default {
  data() {
    return {
      chart: null,
      delayAppear: 0,
      documentWidth,
    }
  },
  watch: {
    data() {
      if (this.chart) {
        this.chart.setOption(this.option, true)
      }
    },
  },
  created() {
    this.resizeHandler = debounce(() => this.chartResize(), 500)
    window.addEventListener('resize', this.resizeHandler)
    setTimeout(() => {
      this.getChart()
    }, this.delayAppear)
  },
  updated() {
    // this.$nextTick(() => {
    //   if (!this.data || this.data?.length === 0) {
    //     this.destroyChart()
    //   } else if (!this.chart) {
    //     this.getChart()
    //   }
    // })
  },
  beforeDestroy() {
    this.destroyChart()
  },
  methods: {
    getChart() {
      if (!this.$refs['chart-container']) {
        return false
      }
      if (this.chart) this.chart.dispose()
      let chart = this.$echarts.init(this.$refs['chart-container'])
      this.chart = chart
      chart.setOption(this.option, true)
      chart.on('click', (params) => {
        if (params.seriesName === '事件分布' && params.data) {
          // 触发 Vue 组件方法
          this.handleDetail(params.data);
        }
      });

      this.$nextTick(this.resizeHandler)
    },
    destroyChart() {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
        }

        window.removeEventListener('resize', this.resizeHandler)
      })
    },
    chartResize() {
      this.documentWidth = document.documentElement.clientWidth
      if (this.chart) {
        this.chart.resize()
      }
    },
    nowSize(val, initWidth = 1920) {
      let documentWidth = this.documentWidth || 1920
      return val * (documentWidth / initWidth);
    },
  },
}
