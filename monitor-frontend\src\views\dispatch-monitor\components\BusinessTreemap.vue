<template>
  <div class="business-treemap">
    <div 
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="treemap-container"
      ref="treemap-container"
    ></div>
    
    <div v-if="!loading && (!businessData || businessData.length === 0)" class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import { getBusinessLabel } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";

export default {
  name: "BusinessTreemap",
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      loading: false,
      businessData: [],
      delayAppear: 1100
    };
  },
  computed: {
    // 计算总数量
    totalCount() {
      if (!this.businessData || this.businessData.length === 0) return 0;
      return this.businessData.reduce((total, item) => {
        return total + parseInt(item.NUM || 0);
      }, 0);
    },
    
    option() {
      return {
        title: {
          text: '业务关注点分布',
          left: 'center',
          top: '10px',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const percentage = this.totalCount > 0 ? ((params.value / this.totalCount) * 100).toFixed(1) : 0;
            return `${params.name}<br/>数量：${params.value.toLocaleString()}<br/>占比：${percentage}%`;
          },
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#4a90e2",
          textStyle: {
            color: "#fff"
          }
        },
        series: [
          {
            name: '业务关注点',
            type: 'treemap',
            width: '95%',
            height: '85%',
            top: '12%',
            left: 'center',
            roam: false, // 禁用缩放和平移
            nodeClick: false, // 禁用点击
            breadcrumb: {
              show: false // 隐藏面包屑
            },
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
              fontSize: 12,
              fontWeight: 'bold',
              formatter: (params) => {
                // 根据面积大小调整标签显示
                const area = params.treePathInfo[params.treePathInfo.length - 1].value;
                if (area < this.totalCount * 0.01) { // 小于1%的不显示标签
                  return '';
                }
                // 长标签进行换行处理
                const name = params.name;
                if (name.length > 8) {
                  const parts = name.split('、');
                  if (parts.length > 1) {
                    return parts.join('\n');
                  } else if (name.length > 12) {
                    return name.substring(0, 8) + '\n' + name.substring(8);
                  }
                }
                return name;
              }
            },
            itemStyle: {
              borderColor: 'rgba(255, 255, 255, 0.2)',
              borderWidth: 1,
              borderRadius: 4
            },
            emphasis: {
              itemStyle: {
                borderColor: '#00ffff',
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: 'rgba(0, 255, 255, 0.5)'
              }
            },
            levels: [
              {
                itemStyle: {
                  borderWidth: 0,
                  gapWidth: 2
                }
              },
              {
                itemStyle: {
                  borderWidth: 1,
                  gapWidth: 1
                },
                colorSaturation: [0.3, 0.8],
                colorMappingBy: 'value'
              }
            ],
            data: this.getTreemapData()
          }
        ]
      };
    }
  },
  created() {
    this.fetchBusinessData();
  },
  methods: {
    async fetchBusinessData() {
      this.loading = true;
      try {
        const [error, response] = await getBusinessLabel();
        
        if (error) {
          console.error("获取业务关注点数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.businessData = [];
        } else if (response && response.data) {
          console.log("业务关注点数据获取成功:", response.data);
          // 按 NUM 从大到小排序
          this.businessData = response.data.sort((a, b) => parseInt(b.NUM) - parseInt(a.NUM));
          console.log("总数量:", this.totalCount);
        } else {
          console.warn("业务关注点数据为空");
          this.businessData = [];
        }
      } catch (error) {
        console.error("获取业务关注点数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.businessData = [];
      } finally {
        this.loading = false;
      }
    },
    
    getTreemapData() {
      if (!this.businessData || this.businessData.length === 0) {
        return [];
      }
      
      return this.businessData.map((item, index) => {
        const value = parseInt(item.NUM || 0);
        const name = item.BUSINESS_LABEL || '未知业务';
        
        // 根据数值大小和位置计算颜色
        const colorIntensity = Math.min(Math.max(value / this.totalCount * 10, 0.3), 1);
        const hue = 200 + (index % 3) * 20; // 蓝色系变化
        const saturation = 60 + colorIntensity * 40;
        const lightness = 40 + (1 - colorIntensity) * 30;
        
        return {
          name: name,
          value: value,
          itemStyle: {
            color: `hsl(${hue}, ${saturation}%, ${lightness}%)`
          }
        };
      });
    },
    
    // 重写 getChart 方法以适配树图容器
    getChart() {
      if (!this.$refs['treemap-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();
      
      let chart = this.$echarts.init(this.$refs['treemap-container']);
      this.chart = chart;
      chart.setOption(this.option, true);
      
      this.$nextTick(this.resizeHandler);
    }
  },
  
  mounted() {
    // 延迟初始化图表
    this.$nextTick(() => {
      setTimeout(() => {
        this.getChart();
      }, this.delayAppear);
    });
  },
  
  updated() {
    this.$nextTick(() => {
      if (this.$refs['treemap-container']) {
        if (!this.chart) {
          this.getChart();
        } else {
          this.chart.setOption(this.option, true);
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.business-treemap {
  flex: 1;
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  
  .treemap-container {
    width: 424px;
    flex: 1;
    min-height: 400px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
  
  .no-data {
    width: 424px;
    flex: 1;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
}
</style>
