<template>
  <div class="business-treemap">
    <div 
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="chart-container"
      ref="chart-container"
    ></div>
    
    <div v-if="!loading && (!businessData || businessData.length === 0)" class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import { getBusinessLabel } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";

export default {
  name: "BusinessTreemap",
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      loading: false,
      businessData: [],
      delayAppear: 1100
    };
  },
  computed: {
    // 计算总数量
    totalCount() {
      if (!this.businessData || this.businessData.length === 0) return 0;
      return this.businessData.reduce((total, item) => {
        return total + parseInt(item.NUM || 0);
      }, 0);
    },
    
    option() {
      return {
        title: {
          text: '业务关注点趋势',
          left: 'center',
          top: '10px',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#4a90e2'
            }
          },
          formatter: (params) => {
            let result = `${params[0].name}<br/>`;
            let total = 0;
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}: ${param.value.toLocaleString()}<br/>`;
              total += param.value;
            });
            result += `<strong>总计: ${total.toLocaleString()}</strong>`;
            return result;
          },
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#4a90e2",
          textStyle: {
            color: "#fff"
          }
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          textStyle: {
            color: '#fff',
            fontSize: 10
          },
          itemWidth: 12,
          itemHeight: 8,
          data: this.getLegendData()
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '20%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.getXAxisData(),
          axisLabel: {
            color: '#fff',
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: '#4a90e2'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '累计数量',
          nameTextStyle: {
            color: '#fff',
            fontSize: 12
          },
          axisLabel: {
            color: '#4a90e2',
            fontSize: 10,
            formatter: (value) => {
              if (value >= 10000) {
                return (value / 10000).toFixed(1) + 'w';
              }
              return value;
            }
          },
          axisLine: {
            lineStyle: {
              color: '#4a90e2'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: this.getSeriesData()
      };
    }
  },
  created() {
    this.fetchBusinessData();
  },
  methods: {
    async fetchBusinessData() {
      this.loading = true;
      try {
        const [error, response] = await getBusinessLabel();
        
        if (error) {
          console.error("获取业务关注点数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.businessData = [];
        } else if (response && response.data) {
          console.log("业务关注点数据获取成功:", response.data);
          // 按 NUM 从大到小排序
          this.businessData = response.data.sort((a, b) => parseInt(b.NUM) - parseInt(a.NUM));
          console.log("总数量:", this.totalCount);
        } else {
          console.warn("业务关注点数据为空");
          this.businessData = [];
        }
      } catch (error) {
        console.error("获取业务关注点数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.businessData = [];
      } finally {
        this.loading = false;
      }
    },
    
    // 获取图例数据（取前10个主要业务）
    getLegendData() {
      if (!this.businessData || this.businessData.length === 0) {
        return [];
      }

      return this.businessData.slice(0, 10).map(item => item.BUSINESS_LABEL || '未知业务');
    },

    // 获取X轴数据（模拟时间序列，这里用排名代替）
    getXAxisData() {
      // 创建一个模拟的时间序列，显示累计效果
      return ['第1位', '前2位', '前3位', '前5位', '前10位', '前15位', '前20位', '全部'];
    },

    // 获取系列数据（堆叠折线图）
    getSeriesData() {
      if (!this.businessData || this.businessData.length === 0) {
        return [];
      }

      // 系统主题色彩
      const colors = [
        '#00ffff', '#4a90e2', '#00aaff', '#0088cc', '#66ccff',
        '#3399ff', '#1177dd', '#55bbee', '#2266bb', '#4488dd'
      ];

      // 取前10个主要业务创建系列
      const topBusinesses = this.businessData.slice(0, 10);
      const xAxisData = this.getXAxisData();

      return topBusinesses.map((business, index) => {
        const name = business.BUSINESS_LABEL || '未知业务';
        const value = parseInt(business.NUM || 0);

        // 创建累计数据：在对应位置显示数值，其他位置为0
        const data = xAxisData.map((_, xIndex) => {
          if (xIndex === 0 && index === 0) return value; // 第1位显示第1个业务
          if (xIndex === 1 && index <= 1) return value; // 前2位显示前2个业务
          if (xIndex === 2 && index <= 2) return value; // 前3位显示前3个业务
          if (xIndex === 3 && index <= 4) return value; // 前5位显示前5个业务
          if (xIndex === 4 && index <= 9) return value; // 前10位显示前10个业务
          if (xIndex === 5 && index <= 14) return value; // 前15位
          if (xIndex === 6 && index <= 19) return value; // 前20位
          if (xIndex === 7) return value; // 全部都显示
          return 0;
        });

        return {
          name: name,
          type: 'line',
          stack: 'total',
          areaStyle: {
            opacity: 0.6
          },
          emphasis: {
            focus: 'series'
          },
          data: data,
          lineStyle: {
            width: 2,
            color: colors[index % colors.length]
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          areaStyle: {
            color: colors[index % colors.length],
            opacity: 0.3
          }
        };
      });
    },
    
    // 重写 getChart 方法以适配堆叠折线图容器
    getChart() {
      if (!this.$refs['chart-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();

      let chart = this.$echarts.init(this.$refs['chart-container']);
      this.chart = chart;
      chart.setOption(this.option, true);

      this.$nextTick(this.resizeHandler);
    }
  },
  
  mounted() {
    // 延迟初始化图表
    this.$nextTick(() => {
      setTimeout(() => {
        this.getChart();
      }, this.delayAppear);
    });
  },
  
  updated() {
    this.$nextTick(() => {
      if (this.$refs['chart-container']) {
        if (!this.chart) {
          this.getChart();
        } else {
          this.chart.setOption(this.option, true);
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.business-treemap {
  flex: 1;
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  
  .chart-container {
    width: 424px;
    flex: 1;
    min-height: 400px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
  
  .no-data {
    width: 424px;
    flex: 1;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
}
</style>
