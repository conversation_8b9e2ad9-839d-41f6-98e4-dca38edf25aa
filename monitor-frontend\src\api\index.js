import { get, post } from '@/http/request'

/**
 * 统一api
 * @param {Object} data 查询参数
 * @returns {Promise} 返回事件列表请求的Promise对象
 */
export function fetchData(params) {
  return post('/dg-portal/webcall', {}, params)
}

/**
 * 左上角-饼状图 (统计投诉分类字段)
 */
export function getRiskCategory(data) {
    return fetchData({...data, action: 'dataScreen.riskCategory'})
}

/**
 * 左下角（统计是否机器人助理接通）
 */
export function getRobotAss(data) {
    return fetchData({...data, action: 'dataScreen.robotAss'})
}

/**
 * 中间部分-柱状图（展示不同时间段内呼叫数量）
 */
export function getCallNumbers(data) {
    return fetchData({...data, action: 'dataScreen.callNumbers'})
}

/**
 * 中间部分柱状图下钻功能
 */
export function getCallNumbersByCallTime(data) {
    return fetchData({...data, action: 'dataScreen.callNumbersByCallTime'})
}

/**
 * 右上角（用户意向统计）
 */
export function getUserIntentionLabel(data) {
    return fetchData({...data, action: 'dataScreen.userIntentionLabel'})
}

/**
 * 右下角（业务关注点）
 */
export function getBusinessLabel(data) {
    return fetchData({...data, action: 'dataScreen.businessLabel'})
}