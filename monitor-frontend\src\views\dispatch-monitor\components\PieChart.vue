<template>
  <div class="pie-chart y-container no-padding">
    <div 
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="pie-chart-container"
      ref="pie-chart-container"
    ></div>
    
    <div v-if="!loading && (!riskCategoryData || riskCategoryData.length === 0)" class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import { getRiskCategory } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";

export default {
  name: "PieChart",
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      loading: false,
      riskCategoryData: [],
      delayAppear: 1100,
      // 系统主题色彩配置
      themeColors: [
        '#00ffff', // 青色 - 主色
        '#4a90e2', // 蓝色 - 辅助色
        '#00aaff', // 亮蓝色
        '#0088cc', // 深蓝色
        '#66ccff', // 浅蓝色
        '#3399ff', // 中蓝色
        '#1177dd', // 深蓝色2
        '#55bbee', // 浅蓝色2
      ]
    };
  },
  computed: {
    option() {
      return {
        title: {
          text: '投诉分类统计',
          left: 'center',
          top: '10px',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#4a90e2",
          textStyle: {
            color: "#fff"
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 8,
          data: this.getLegendData()
        },
        series: [
          {
            name: '投诉分类',
            type: 'pie',
            radius: ['40%', '70%'], // 环形饼图
            center: ['65%', '55%'], // 向右偏移，为图例留空间
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold',
                color: '#fff'
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 255, 255, 0.5)'
              }
            },
            labelLine: {
              show: false
            },
            data: this.getPieData(),
            itemStyle: {
              borderRadius: 4,
              borderColor: 'rgba(0, 0, 0, 0.2)',
              borderWidth: 1
            }
          }
        ]
      };
    }
  },
  created() {
    this.fetchRiskCategory();
  },
  methods: {
    async fetchRiskCategory() {
      this.loading = true;
      try {
        const [error, response] = await getRiskCategory();
        
        if (error) {
          console.error("获取投诉分类数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.riskCategoryData = [];
        } else if (response && response.data) {
          console.log("投诉分类数据获取成功:", response.data);
          this.riskCategoryData = response.data;
          console.log("处理后的饼图数据:", this.getPieData());
        } else {
          console.warn("投诉分类数据为空");
          this.riskCategoryData = [];
        }
      } catch (error) {
        console.error("获取投诉分类数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.riskCategoryData = [];
      } finally {
        this.loading = false;
      }
    },
    
    getLegendData() {
      if (!this.riskCategoryData || this.riskCategoryData.length === 0) {
        return [];
      }

      // 根据实际API返回的数据结构：COMPLAINT_RISK_LABEL
      return this.riskCategoryData.map(item => item.COMPLAINT_RISK_LABEL || '未知分类');
    },
    
    getPieData() {
      if (!this.riskCategoryData || this.riskCategoryData.length === 0) {
        return [];
      }

      // 根据实际API返回的数据结构：COMPLAINT_RISK_LABEL 和 NUM
      return this.riskCategoryData.map((item, index) => {
        const label = item.COMPLAINT_RISK_LABEL || '未知分类';
        let color = this.themeColors[index % this.themeColors.length];

        // 根据风险等级设置更合适的颜色
        if (label.includes('高投诉风险')) {
          color = '#ff6b6b'; // 红色系，表示高风险
        } else if (label.includes('中投诉风险')) {
          color = '#ffa726'; // 橙色系，表示中风险
        } else if (label.includes('低投诉风险')) {
          color = '#00ffff'; // 青色系，表示低风险
        }

        return {
          name: label,
          value: parseInt(item.NUM || 0),
          itemStyle: {
            color: color
          }
        };
      });
    },
    
    // 重写 getChart 方法以适配饼图容器
    getChart() {
      if (!this.$refs['pie-chart-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();
      
      let chart = this.$echarts.init(this.$refs['pie-chart-container']);
      this.chart = chart;
      chart.setOption(this.option, true);
      
      // 添加饼图点击事件（可选）
      chart.on('click', (params) => {
        console.log('饼图点击事件:', params);
        // 可以在这里添加点击交互逻辑
      });
      
      this.$nextTick(this.resizeHandler);
    }
  },
  
  mounted() {
    // 延迟初始化图表
    this.$nextTick(() => {
      setTimeout(() => {
        this.getChart();
      }, this.delayAppear);
    });
  },
  
  updated() {
    this.$nextTick(() => {
      if (this.$refs['pie-chart-container']) {
        if (!this.chart) {
          this.getChart();
        } else {
          this.chart.setOption(this.option, true);
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.pie-chart {
  flex: 0 0 max-content;
  margin-top: 16px;
  margin-bottom: 24px;
  
  .pie-chart-container {
    width: 424px;
    height: 300px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
  
  .no-data {
    width: 424px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
}
</style>
