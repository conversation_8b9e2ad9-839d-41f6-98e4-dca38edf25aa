<template>
  <div class="pie-chart y-container no-padding">
    <div 
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="pie-chart-container"
      ref="pie-chart-container"
    ></div>
    
    <div v-if="!loading && (!riskCategoryData || riskCategoryData.length === 0)" class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import { getRiskCategory } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";

export default {
  name: "PieChart",
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      loading: false,
      riskCategoryData: [],
      delayAppear: 1100,
      // 系统主题色彩配置
      themeColors: [
        '#00ffff', // 青色 - 主色
        '#4a90e2', // 蓝色 - 辅助色
        '#00aaff', // 亮蓝色
        '#0088cc', // 深蓝色
        '#66ccff', // 浅蓝色
        '#3399ff', // 中蓝色
        '#1177dd', // 深蓝色2
        '#55bbee', // 浅蓝色2
      ]
    };
  },
  computed: {
    option() {
      if (!this.riskCategoryData || this.riskCategoryData.length === 0) {
        return {};
      }

      const pieData = this.riskCategoryData.map((item, index) => {
        const label = item.COMPLAINT_RISK_LABEL || '未知分类';
        let baseColor = this.themeColors[index % this.themeColors.length];

        // 根据风险等级设置颜色
        if (label.includes('高投诉风险')) {
          baseColor = '#ff6b6b';
        } else if (label.includes('中投诉风险')) {
          baseColor = '#ffa726';
        } else if (label.includes('低投诉风险')) {
          baseColor = '#00ffff';
        }

        return {
          name: label,
          value: parseInt(item.NUM || 0),
          itemStyle: {
            color: baseColor,
            opacity: 0.9
          }
        };
      });

      const pie3DOption = this.getPie3D(pieData);

      return {
        title: {
          text: '投诉分类统计',
          left: 'center',
          top: '10px',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            console.log('Tooltip params:', params); // 调试信息

            // 从组件数据中查找对应的风险分类数据
            let name = params.seriesName || '未知分类';
            let value = 0;
            let color = params.color || '#00ffff';

            // 在原始数据中查找匹配的项
            const matchedItem = this.riskCategoryData.find(item => {
              const label = item.COMPLAINT_RISK_LABEL || '未知分类';
              return label === name;
            });

            if (matchedItem) {
              name = matchedItem.COMPLAINT_RISK_LABEL || '未知分类';
              value = parseInt(matchedItem.NUM || 0);

              // 根据风险等级设置颜色
              if (name.includes('高投诉风险')) {
                color = '#ff6b6b';
              } else if (name.includes('中投诉风险')) {
                color = '#ffa726';
              } else if (name.includes('低投诉风险')) {
                color = '#00ffff';
              }
            }

            // 计算总数和占比
            const total = this.riskCategoryData.reduce((sum, item) => sum + parseInt(item.NUM || 0), 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;

            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; color: #00ffff; margin-bottom: 4px;">
                  投诉分类统计
                </div>
                <div style="color: #fff; margin-bottom: 2px;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 6px;"></span>
                  ${name}
                </div>
                <div style="color: #fff;">
                  数量: <span style="color: #00ffff; font-weight: bold;">${value.toLocaleString()}</span>
                </div>
                <div style="color: #fff;">
                  占比: <span style="color: #00ffff; font-weight: bold;">${percentage}%</span>
                </div>
              </div>
            `;
          },
          backgroundColor: "rgba(0, 0, 0, 0.85)",
          borderColor: "#00ffff",
          borderWidth: 1,
          textStyle: {
            color: "#fff"
          },
          extraCssText: 'border-radius: 6px; box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 8,
          data: this.getLegendData(),
          selectedMode: false
        },
        ...pie3DOption
      };
    }
  },
  created() {
    this.fetchRiskCategory();
  },
  methods: {
    async fetchRiskCategory() {
      this.loading = true;
      try {
        const [error, response] = await getRiskCategory();
        
        if (error) {
          console.error("获取投诉分类数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.riskCategoryData = [];
        } else if (response && response.data) {
          console.log("投诉分类数据获取成功:", response.data);
          this.riskCategoryData = response.data;
          console.log("处理后的饼图数据:", this.getPieData());
        } else {
          console.warn("投诉分类数据为空");
          this.riskCategoryData = [];
        }
      } catch (error) {
        console.error("获取投诉分类数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.riskCategoryData = [];
      } finally {
        this.loading = false;
      }
    },
    
    getLegendData() {
      if (!this.riskCategoryData || this.riskCategoryData.length === 0) {
        return [];
      }

      // 根据实际API返回的数据结构：COMPLAINT_RISK_LABEL
      return this.riskCategoryData.map(item => item.COMPLAINT_RISK_LABEL || '未知分类');
    },
    
    getPieData() {
      if (!this.riskCategoryData || this.riskCategoryData.length === 0) {
        return [];
      }

      // 根据实际API返回的数据结构：COMPLAINT_RISK_LABEL 和 NUM
      return this.riskCategoryData.map((item, index) => {
        const label = item.COMPLAINT_RISK_LABEL || '未知分类';
        let baseColor = this.themeColors[index % this.themeColors.length];

        // 根据风险等级设置更合适的颜色
        if (label.includes('高投诉风险')) {
          baseColor = '#ff6b6b'; // 红色系，表示高风险
        } else if (label.includes('中投诉风险')) {
          baseColor = '#ffa726'; // 橙色系，表示中风险
        } else if (label.includes('低投诉风险')) {
          baseColor = '#00ffff'; // 青色系，表示低风险
        }

        // 创建3D渐变效果
        const gradientColor = {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: this.lightenColor(baseColor, 0.3) // 顶部更亮
            },
            {
              offset: 0.5,
              color: baseColor // 中间原色
            },
            {
              offset: 1,
              color: this.darkenColor(baseColor, 0.3) // 底部更暗，营造立体感
            }
          ]
        };

        return {
          name: label,
          value: parseInt(item.NUM || 0),
          itemStyle: {
            color: gradientColor,
            // 3D立体边框效果
            borderColor: this.darkenColor(baseColor, 0.5),
            borderWidth: 2,
            shadowBlur: 12,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        };
      });
    },

    // 颜色加亮函数
    lightenColor(color, amount) {
      const usePound = color[0] === '#';
      const col = usePound ? color.slice(1) : color;
      const num = parseInt(col, 16);
      let r = (num >> 16) + amount * 255;
      let g = (num >> 8 & 0x00FF) + amount * 255;
      let b = (num & 0x0000FF) + amount * 255;
      r = r > 255 ? 255 : r < 0 ? 0 : r;
      g = g > 255 ? 255 : g < 0 ? 0 : g;
      b = b > 255 ? 255 : b < 0 ? 0 : b;
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    },

    // 颜色加深函数
    darkenColor(color, amount) {
      const usePound = color[0] === '#';
      const col = usePound ? color.slice(1) : color;
      const num = parseInt(col, 16);
      let r = (num >> 16) - amount * 255;
      let g = (num >> 8 & 0x00FF) - amount * 255;
      let b = (num & 0x0000FF) - amount * 255;
      r = r > 255 ? 255 : r < 0 ? 0 : r;
      g = g > 255 ? 255 : g < 0 ? 0 : g;
      b = b > 255 ? 255 : b < 0 ? 0 : b;
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    },

    // 获取参数化方程
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      // 计算中点比例
      let midRatio = (startRatio + endRatio) / 2;

      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;

      // 禁用选中效果
      isSelected = false;

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== 'undefined' ? k : 1 / 3;

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.cos(midRadian) * 0.1 : 0;

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },
        x: function (u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1;
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
        },
      };
    },

    // 生成3D饼图
    getPie3D(pieData, internalDiameterRatio) {
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 4;

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
          type: 'surface',
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: 1 / 10,
          },
        };

        if (typeof pieData[i].itemStyle != 'undefined') {
          let itemStyle = {};
          typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;
          typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null;
          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          false,
          k,
          series[i].pieData.value
        );

        startValue = endValue;
      }

      return {
        xAxis3D: {},
        yAxis3D: {},
        zAxis3D: {},
        grid3D: {
          left: '35%',  // 向右移动，为左侧图例留出空间
          top: '-5%',   // 稍微向下调整
          width: '60%', // 保持宽度
          show: false,
          boxHeight: 40,
          viewControl: {
            distance: 150,
            alpha: 25,
            beta: 40,
            autoRotate: false,
            rotateSensitivity: 1,
            zoomSensitivity: 1,
            panSensitivity: 1,
            minAlpha: 5,
            maxAlpha: 90,
            minBeta: -360,
            maxBeta: 360,
            minDistance: 100,
            maxDistance: 400
          },
        },
        series: series,
      };
    },

    // 生成3D饼图系列
    get3DPieSeries() {
      if (!this.riskCategoryData || this.riskCategoryData.length === 0) {
        return [];
      }

      const pieData = this.riskCategoryData.map((item, index) => {
        const label = item.COMPLAINT_RISK_LABEL || '未知分类';
        let baseColor = this.themeColors[index % this.themeColors.length];

        // 根据风险等级设置颜色
        if (label.includes('高投诉风险')) {
          baseColor = '#ff6b6b';
        } else if (label.includes('中投诉风险')) {
          baseColor = '#ffa726';
        } else if (label.includes('低投诉风险')) {
          baseColor = '#00ffff';
        }

        return {
          name: label,
          value: parseInt(item.NUM || 0),
          itemStyle: {
            color: baseColor,
            opacity: 0.9
          }
        };
      });

      return this.getPie3D(pieData);
    },
    
    // 重写 getChart 方法以适配饼图容器
    getChart() {
      if (!this.$refs['pie-chart-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();
      
      let chart = this.$echarts.init(this.$refs['pie-chart-container']);
      this.chart = chart;
      chart.setOption(this.option, true);
      
      // 添加饼图点击事件（可选）
      chart.on('click', (params) => {
        console.log('饼图点击事件:', params);
        // 可以在这里添加点击交互逻辑
      });
      
      this.$nextTick(this.resizeHandler);
    }
  },
  
  mounted() {
    // 延迟初始化图表
    this.$nextTick(() => {
      setTimeout(() => {
        this.getChart();
      }, this.delayAppear);
    });
  },
  
  updated() {
    this.$nextTick(() => {
      if (this.$refs['pie-chart-container']) {
        if (!this.chart) {
          this.getChart();
        } else {
          this.chart.setOption(this.option, true);
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.pie-chart {
  flex: 0 0 max-content;
  margin-top: 16px;
  margin-bottom: 24px;
  
  .pie-chart-container {
    width: 424px;
    height: 350px; // 增加高度以适配3D效果
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }

  .no-data {
    width: 424px;
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
}
</style>
