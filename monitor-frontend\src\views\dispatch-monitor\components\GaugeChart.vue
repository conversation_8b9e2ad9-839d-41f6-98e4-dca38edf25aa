<template>
  <div class="gauge-chart y-container no-padding">
    <div 
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="gauge-chart-container"
      ref="gauge-chart-container"
    ></div>
    
    <div v-if="!loading && (!robotAssData || robotAssData.length === 0)" class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import { getRobotAss } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";

export default {
  name: "GaugeChart",
  mixins: [chartMixins],
  inject: ["dateRange"],
  data() {
    return {
      loading: false,
      robotAssData: [],
      delayAppear: 1100
    };
  },
  computed: {
    // 计算机器人接通率
    robotConnectRate() {
      if (!this.robotAssData || this.robotAssData.length === 0) {
        return 0;
      }

      let totalCount = 0;
      let robotConnectedCount = 0;

      this.robotAssData.forEach(item => {
        const count = parseInt(item.NUM || 0);
        totalCount += count;

        // 根据实际API返回的数据结构：ROBOT_ASS_TAG
        const status = item.ROBOT_ASS_TAG || '';
        if (status === '是') {
          robotConnectedCount += count;
        }
      });

      return totalCount > 0 ? Math.round((robotConnectedCount / totalCount) * 100) : 0;
    },
    
    // 获取总数量
    totalCount() {
      if (!this.robotAssData || this.robotAssData.length === 0) {
        return 0;
      }
      
      return this.robotAssData.reduce((total, item) => {
        return total + parseInt(item.NUM || 0);
      }, 0);
    },
    
    option() {
      return {
        title: {
          text: '机器人接通统计',
          left: 'center',
          top: '10px',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            const data = params[0];
            const total = this.totalCount;
            const percentage = total > 0 ? ((data.value / total) * 100).toFixed(2) : 0;
            return `${data.name}<br/>数量：${data.value.toLocaleString()}<br/>占比：${percentage}%`;
          },
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#4a90e2",
          textStyle: {
            color: "#fff"
          }
        },
        grid: {
          left: '15%',
          right: '15%',
          bottom: '15%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.getBarLabels(),
          axisLabel: {
            color: '#fff',
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLine: {
            lineStyle: {
              color: '#4a90e2'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '数量',
          nameTextStyle: {
            color: '#fff',
            fontSize: 12
          },
          axisLabel: {
            color: '#4a90e2',
            fontSize: 12,
            formatter: (value) => {
              if (value >= 10000) {
                return (value / 10000).toFixed(1) + 'w';
              }
              return value;
            }
          },
          axisLine: {
            lineStyle: {
              color: '#4a90e2'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: this.getBarData(),
            barWidth: '60%',
            itemStyle: {
              borderRadius: [4, 4, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              fontSize: 12,
              fontWeight: 'bold',
              formatter: (params) => {
                return params.value.toLocaleString();
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 255, 255, 0.5)'
              }
            }
          }
        ]
      };
    }
  },
  created() {
    this.fetchRobotAss();
  },
  methods: {
    async fetchRobotAss() {
      this.loading = true;
      try {
        const [error, response] = await getRobotAss();
        
        if (error) {
          console.error("获取机器人接通数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.robotAssData = [];
        } else if (response && response.data) {
          console.log("机器人接通数据获取成功:", response.data);
          this.robotAssData = response.data;

          // 详细的数据处理日志
          let robotCount = 0;
          let totalCount = 0;
          response.data.forEach(item => {
            const count = parseInt(item.NUM || 0);
            totalCount += count;
            console.log(`${item.ROBOT_ASS_TAG}: ${count}`);
            if (item.ROBOT_ASS_TAG === '是') {
              robotCount += count;
            }
          });

          console.log("机器人接通数:", robotCount);
          console.log("总呼叫数:", totalCount);
          console.log("计算的接通率:", this.robotConnectRate + "%");
        } else {
          console.warn("机器人接通数据为空");
          this.robotAssData = [];
        }
      } catch (error) {
        console.error("获取机器人接通数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.robotAssData = [];
      } finally {
        this.loading = false;
      }
    },
    
    // 获取柱状图标签
    getBarLabels() {
      if (!this.robotAssData || this.robotAssData.length === 0) {
        return ['是', '否'];
      }

      return this.robotAssData.map(item => item.ROBOT_ASS_TAG || '未知');
    },

    // 获取柱状图数据
    getBarData() {
      if (!this.robotAssData || this.robotAssData.length === 0) {
        return [0, 0];
      }

      return this.robotAssData.map((item) => {
        const value = parseInt(item.NUM || 0);
        const tag = item.ROBOT_ASS_TAG || '';

        // 使用系统主题色彩
        let color;
        if (tag === '是') {
          color = '#00ffff'; // 青色 - 系统主色，表示机器人接通
        } else if (tag === '否') {
          color = '#4a90e2'; // 蓝色 - 系统辅助色，表示人工接通
        } else {
          color = '#0088cc'; // 深蓝色 - 系统主题色之一
        }

        return {
          value: value,
          itemStyle: {
            color: color
          }
        };
      });
    },
    
    // 重写 getChart 方法以适配柱状图容器
    getChart() {
      if (!this.$refs['gauge-chart-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();

      let chart = this.$echarts.init(this.$refs['gauge-chart-container']);
      this.chart = chart;
      chart.setOption(this.option, true);

      this.$nextTick(this.resizeHandler);
    }
  },
  
  mounted() {
    // 延迟初始化图表
    this.$nextTick(() => {
      setTimeout(() => {
        this.getChart();
      }, this.delayAppear);
    });
  },
  
  updated() {
    this.$nextTick(() => {
      if (this.$refs['gauge-chart-container']) {
        if (!this.chart) {
          this.getChart();
        } else {
          this.chart.setOption(this.option, true);
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.gauge-chart {
  flex: 1; // 占满剩余空间
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;

  .gauge-chart-container {
    width: 424px;
    flex: 1; // 高度自适应剩余空间
    min-height: 400px; // 最小高度保证
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }

  .no-data {
    width: 424px;
    flex: 1;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    background: linear-gradient(0deg, rgba(0, 128, 255, 0) 0%, rgba(0, 128, 255, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 170, 255, 0.2);
  }
}
</style>
