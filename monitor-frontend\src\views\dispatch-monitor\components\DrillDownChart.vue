<template>
  <div v-if="dialogVisible" class="drill-down-dialog-mask">
    <Dialog
      title="通话时长分布详情"
      :visible.sync="dialogVisible"
      :header-type="1"
      @close="handleClose"
    >
      <div class="drill-down-content">
        <div class="chart-title">
          <span>时间段：{{ selectedTimeRange }}</span>
          <span class="subtitle">通话时长分布统计</span>
        </div>

        <div
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          class="drill-chart-container"
          ref="drill-chart-container"
        ></div>

        <div v-if="!loading && (!drillDownData || drillDownData.length === 0)" class="no-data">
          暂无数据
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <button @click="handleClose" class="custom-button">关闭</button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script>
import { getCallNumbersByCallTime } from "@/api";
import chartMixins from "@/mixins/chartMixins.js";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "DrillDownChart",
  components: {
    Dialog
  },
  mixins: [chartMixins],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedTimeRange: {
      type: String,
      default: ""
    },
    selectedData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      drillDownData: [],
      // 动态时间段标签，从API返回数据中获取
      timeLabels: []
    };
  },
  computed: {
    option() {
      return {
        title: {
          text: "",
          left: "center",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: (params) => {
            const data = params[0];
            return `${data.name}<br/>挂断数量：${data.value}`;
          },
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#4a90e2",
          textStyle: {
            color: "#fff"
          }
        },
        grid: {
          left: "10%",
          right: "10%",
          bottom: "15%",
          top: "10%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          name: '通话时长占比',
          data: this.timeLabels,
          axisLabel: {
            color: "#fff",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "#4a90e2",
            },
          },
          nameTextStyle: {
            color: "#fff",
            fontSize: 12
          }
        },
        yAxis: {
          type: "value",
          name: "挂断数量",
          axisLabel: {
            color: "#4a90e2",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "#4a90e2",
            },
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255, 255, 255, 0.1)",
            },
          },
          nameTextStyle: {
            color: "#fff",
            fontSize: 12
          }
        },
        series: [
          {
            type: "bar",
            data: this.getChartData(),
            itemStyle: {
              color: "#4a90e2",
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: "#00ffff", // 青色，与系统主题更搭配
              },
            },
            barWidth: "60%"
          },
        ],
      };
    },
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
        if (newVal && this.selectedTimeRange) {
          this.fetchDrillDownData();
        }
      },
      immediate: true
    },
    selectedTimeRange: {
      handler(newVal) {
        if (newVal && this.dialogVisible) {
          // this.fetchDrillDownData();
        }
      }
    }
  },
  methods: {
    async fetchDrillDownData() {
      if (!this.selectedTimeRange) return;
      
      this.loading = true;
      try {
        // 构造API参数，传递选中的时间段信息
        const params = {
          generationTime: this.selectedTimeRange,
        };
        
        const [error, response] = await getCallNumbersByCallTime(params);
        
        if (error) {
          console.error("获取下钻数据失败:", error);
          this.$message.error("获取数据失败，请稍后重试");
          this.drillDownData = [];
        } else if (response && response.data) {
          console.log("下钻数据获取成功:", response.data);
          this.drillDownData = response.data;
          // 动态提取时间段标签
          this.timeLabels = response.data.map(item => item.CALL_TIME_RANGE);
          console.log("动态时间段标签:", this.timeLabels);
        } else {
          console.warn("下钻数据为空");
          this.drillDownData = [];
        }
      } catch (error) {
        console.error("获取下钻数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.drillDownData = [];
      } finally {
        this.loading = false;
      }
    },
    
    getChartData() {
      // 直接从API返回的数据中提取数值
      if (!this.drillDownData || this.drillDownData.length === 0) {
        console.log("下钻数据为空，返回默认数据");
        return [];
      }

      // 直接按API返回的顺序提取数值
      const chartData = this.drillDownData.map(item => parseInt(item.RECORD_COUNT) || 0);
      console.log("图表数据:", chartData);

      return chartData;
    },
    
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    
    // 重写 getChart 方法以适配弹窗容器
    getChart() {
      if (!this.$refs['drill-chart-container']) {
        return false;
      }
      if (this.chart) this.chart.dispose();

      let chart = this.$echarts.init(this.$refs['drill-chart-container']);
      this.chart = chart;
      chart.setOption(this.option, true);

      this.$nextTick(this.resizeHandler);
    },


  },
  
  mounted() {
    // 延迟初始化图表，确保弹窗已完全显示
    this.$nextTick(() => {
      if (this.dialogVisible) {
        setTimeout(() => {
          this.getChart();
        }, 300);
      }
    });
  },
  
  updated() {
    this.$nextTick(() => {
      if (this.dialogVisible && this.$refs['drill-chart-container']) {
        if (!this.chart) {
          this.getChart();
        } else {
          this.chart.setOption(this.option, true);
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.drill-down-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1999; // 确保在 Dialog 的 z-index 之下
  display: flex;
  align-items: center;
  justify-content: center;
}

.drill-down-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-title {
    text-align: center;
    margin-bottom: 20px;
    color: #fff;
    flex-shrink: 0;

    span {
      display: block;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .subtitle {
      font-size: 14px;
      color: #a1e0ff;
      font-weight: normal;
    }
  }

  .drill-chart-container {
    width: 100%;
    height: 600px; // 适配大尺寸 Dialog
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    flex: 1;
    min-height: 500px;
  }

  .no-data {
    text-align: center;
    color: #999;
    font-size: 14px;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }
}

.dialog-footer {
  padding: 0 20px;

  .custom-button {
    background: linear-gradient(135deg, #00aaff 0%, #0088cc 100%);
    border: 1px solid #00aaff;
    color: #fff;
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    letter-spacing: 1px;

    &:hover {
      background: linear-gradient(135deg, #0088cc 0%, #006699 100%);
      border-color: #0088cc;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 170, 255, 0.3);
    }

    &:focus {
      outline: none;
      background: linear-gradient(135deg, #00aaff 0%, #0088cc 100%);
      border-color: #00aaff;
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>
